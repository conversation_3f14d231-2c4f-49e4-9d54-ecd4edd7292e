#include <iostream>
#include <cstring>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <cstdio>

using namespace std;

#define PORT 12345
#define BUF_SIZE 1024

int main() {
    int serverSocket = socket(AF_INET, SOCK_DGRAM, 0);
    if (serverSocket < 0) {
        perror("socket 创建失败");
        return 1;
    }

    sockaddr_in serverAddr{};
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(PORT);
    serverAddr.sin_addr.s_addr = INADDR_ANY;

    if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        perror("bind 失败");
        close(serverSocket);
        return 1;
    }

    cout << "服务端已启动，等待文件传输..." << endl;

    FILE *fp = fopen("received_file.txt", "wb");
    if (!fp) {
        perror("打开文件失败");
        close(serverSocket);
        return 1;
    }

    sockaddr_in clientAddr{};
    socklen_t clientAddrLen = sizeof(clientAddr);
    char buffer[BUF_SIZE];
    ssize_t recvLen;

    while ((recvLen = recvfrom(serverSocket, buffer, BUF_SIZE, 0,
                               (struct sockaddr*)&clientAddr, &clientAddrLen)) > 0) {
        // 若收到 "EOF"，结束传输
        if (recvLen == 3 && strncmp(buffer, "EOF", 3) == 0) {
            cout << "收到 EOF，文件传输结束" << endl;
            break;
        }
        fwrite(buffer, 1, recvLen, fp);
    }

    fclose(fp);
    close(serverSocket);
    cout << "文件已保存为 received_file.txt" << endl;
    return 0;
}
