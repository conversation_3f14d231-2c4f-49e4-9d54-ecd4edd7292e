#include <iostream>
#include <cstring>
#include <unistd.h>
#include <arpa/inet.h>
#include <cstdio>

using namespace std;

#define SERVER_IP "***********"  
#define PORT 12345
#define BUF_SIZE 1024

int main() {
    int clientSocket = socket(AF_INET, SOCK_DGRAM, 0);
    if (clientSocket < 0) {
        perror("socket 创建失败");
        return 1;
    }

    sockaddr_in serverAddr{};
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(PORT);
    serverAddr.sin_addr.s_addr = inet_addr(SERVER_IP);

    FILE *fp = fopen("send_file.txt", "rb");
    if (!fp) {
        perror("打开文件失败");
        close(clientSocket);
        return 1;
    }

    char buffer[BUF_SIZE];
    size_t bytesRead;
    while ((bytesRead = fread(buffer, 1, BUF_SIZE, fp)) > 0) {
        sendto(clientSocket, buffer, bytesRead, 0,
               (struct sockaddr*)&serverAddr, sizeof(serverAddr));
    }

    // 发送“EOF”表示传输完成
    sendto(clientSocket, "EOF", 3, 0, (struct sockaddr*)&serverAddr, sizeof(serverAddr));

    fclose(fp);
    close(clientSocket);
    cout << "文件已发送" << endl;
    return 0;
}
